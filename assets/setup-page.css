/* Setup Page Specific Styles */

.setup-breadcrumb {
    margin-bottom: 20px;
    padding: 10px;
    background: #f9f9f9;
    border-left: 4px solid #0073aa;
    border-radius: 4px;
}

.setup-breadcrumb p {
    margin: 0;
}

.hidden {
    display: none;
}

/* Step Progress Indicator */
.setup-progress-indicator {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    padding: 0 20px;
}

.step-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;
    opacity: 0.5;
    transition: opacity 0.3s ease;
}

.step-indicator.active {
    opacity: 1;
}

.step-indicator.completed {
    opacity: 1;
}

.step-indicator:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 15px;
    left: 60%;
    right: -40%;
    height: 2px;
    background: #ddd;
    z-index: 1;
}

.step-indicator.completed:not(:last-child)::after {
    background: #0073aa;
}

.step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #ddd;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.step-indicator.active .step-number {
    background: #0073aa;
    color: white;
}

.step-indicator.completed .step-number {
    background: #00a32a;
    color: white;
}

.step-title {
    font-size: 12px;
    text-align: center;
    color: #666;
    font-weight: 500;
}

.step-indicator.active .step-title {
    color: #0073aa;
    font-weight: 600;
}

.step-indicator.completed .step-title {
    color: #00a32a;
    font-weight: 600;
}

.setup-step {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    display: none;
}

.setup-step.active {
    display: block;
}

.setup-step h2 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

#setup-status {
    background: #f1f1f1;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

/* Enhanced Setup Status Styles */
.setup-type-info {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

.setup-type-info h3 {
    margin: 0 0 10px 0;
    color: #0073aa;
    font-size: 18px;
}

.setup-description {
    margin: 0;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

.setup-status-details {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

.setup-status-details h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 14px;
    font-weight: 600;
}

.setup-warnings {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

.setup-warnings h4 {
    margin: 0 0 10px 0;
    color: #856404;
    font-size: 14px;
    font-weight: 600;
}

.setup-warnings ul {
    margin: 0;
    padding-left: 20px;
}

.setup-warnings li {
    color: #856404;
    margin-bottom: 5px;
}

.setup-next-steps {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

.setup-next-steps h4 {
    margin: 0 0 10px 0;
    color: #155724;
    font-size: 14px;
    font-weight: 600;
}

.setup-next-steps ol {
    margin: 0;
    padding-left: 20px;
}

.setup-next-steps li {
    color: #155724;
    margin-bottom: 5px;
    line-height: 1.4;
}

.existing-repo-notice {
    margin-bottom: 20px;
}

/* Initial Status Section */
.initial-status-section {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.initial-status-section h3 {
    margin: 0 0 15px 0;
    color: #0073aa;
    font-size: 16px;
}

.initial-status-result {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-top: 15px;
}

.initial-status-result h4 {
    margin: 0 0 10px 0;
    color: #0073aa;
    font-size: 16px;
}

.status-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin: 15px 0;
}

.status-summary .status-item {
    margin-bottom: 0;
}

.status-item.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.status-warnings {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 15px;
    margin-top: 15px;
}

.status-warnings h5 {
    margin: 0 0 10px 0;
    color: #856404;
    font-size: 14px;
    font-weight: 600;
}

.status-warnings ul {
    margin: 0;
    padding-left: 20px;
}

.status-warnings li {
    color: #856404;
    margin-bottom: 5px;
}

/* Setup Mode Selector */
.setup-mode-section {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.setup-mode-section h3 {
    margin: 0 0 15px 0;
    color: #0073aa;
    font-size: 16px;
}

.setup-mode-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.setup-mode-option {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    background: #fff;
    border: 2px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.setup-mode-option:hover {
    border-color: #0073aa;
    background: #f0f8ff;
}

.setup-mode-option input[type="radio"] {
    margin: 0 12px 0 0;
    flex-shrink: 0;
}

.setup-mode-option input[type="radio"]:checked + .option-title {
    color: #0073aa;
    font-weight: 600;
}

.setup-mode-option input[type="radio"]:checked {
    accent-color: #0073aa;
}

.setup-mode-option:has(input[type="radio"]:checked) {
    border-color: #0073aa;
    background: #f0f8ff;
}

.option-title {
    display: block;
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
}

.option-description {
    display: block;
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

/* Responsive adjustments for setup mode */
@media (max-width: 768px) {
    .setup-mode-option {
        flex-direction: column;
        align-items: flex-start;
    }

    .setup-mode-option input[type="radio"] {
        margin: 0 0 10px 0;
    }
}



.status-item {
    margin-bottom: 10px;
    padding: 5px 10px;
    border-radius: 3px;
}

.status-item.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-item.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-item.warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

#git-path-status {
    margin-top: 10px;
}

.progress-item {
    margin-bottom: 10px;
    padding: 8px 12px;
    border-radius: 3px;
    background: #f1f1f1;
    border: 1px solid #ddd;
}

.progress-item.in-progress {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.progress-item.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.progress-item.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Step 4 Success Message */
.setup-success-message {
    font-size: 16px;
    color: #155724;
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 4px;
    padding: 15px 20px;
    margin: 20px 0;
    font-weight: 500;
}

#step-4 h2 {
    color: #155724;
    font-size: 24px;
    margin-bottom: 10px;
}

/* Setup Summary */
.setup-summary {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
}

.setup-summary h3 {
    margin-top: 0;
    color: #155724;
}

.setup-summary ul {
    list-style: none;
    padding: 0;
    margin: 10px 0 0 0;
}

.setup-summary li {
    padding: 5px 0;
    color: #155724;
    font-weight: 500;
}

/* Setup Navigation */
.setup-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

/* Step 4 specific styling */
#step-4 .setup-navigation {
    text-align: center;
    flex-direction: column;
    gap: 15px;
}

#step-4 .setup-navigation h3 {
    margin: 0 0 10px 0;
}

#step-4 .setup-navigation p {
    margin: 0;
}

#step-4 .button-large {
    padding: 8px 16px;
    height: auto;
    line-height: 1.5;
    margin: 0 5px;
}

.setup-navigation .button {
    min-width: 120px;
}

.setup-navigation .button:only-child {
    margin-left: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .setup-progress-indicator {
        flex-direction: column;
        gap: 10px;
    }

    .step-indicator:not(:last-child)::after {
        display: none;
    }

    .setup-navigation {
        flex-direction: column;
        gap: 10px;
    }

    .setup-navigation .button {
        width: 100%;
    }
}
