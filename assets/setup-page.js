/**
 * Git Manager Setup Page JavaScript
 */
jQuery(document).ready(function($) {
    'use strict';
    
    const SetupPage = {
        currentStep: 1,

        init: function() {
            this.bindEvents();
            this.showStep(1);
        },

        bindEvents: function() {
            $('#check-current-setup').on('click', this.handleInitialStatusCheck);
            $('#test-git').on('click', this.handleTestGit);
            $('#check-setup').on('click', this.handleCheckSetup);
            $('#start-setup').on('click', this.handleStartSetup);
            $('#complete-setup').on('click', this.handleCompleteSetup);



            // Step navigation
            $('#next-step-1').on('click', function () { SetupPage.nextStep(); });
            $('#prev-step-2').on('click', function () { SetupPage.prevStep(); });
            $('#prev-step-2-progress').on('click', function () { SetupPage.prevStep(); });
            $('#prev-step-3').on('click', function () { SetupPage.prevStep(); });
        },

        showStep: function (stepNumber) {
            // Hide all steps
            $('.setup-step').removeClass('active');
            $('.step-indicator').removeClass('active completed');

            // Show current step
            $('#step-' + stepNumber).addClass('active');

            // Update progress indicator
            for (let i = 1; i <= 4; i++) {
                if (i < stepNumber) {
                    $('.step-indicator[data-step="' + i + '"]').addClass('completed');
                } else if (i === stepNumber) {
                    $('.step-indicator[data-step="' + i + '"]').addClass('active');
                }
            }

            this.currentStep = stepNumber;
        },

        nextStep: function () {
            if (this.currentStep < 4) {
                this.showStep(this.currentStep + 1);
            }
        },

        prevStep: function () {
            if (this.currentStep > 1) {
                this.showStep(this.currentStep - 1);
            }
        },

        handleTestGit: function() {
            var gitPath = $('#git-path').val();
            var button = $(this);

            button.prop('disabled', true).text('Testing...');

            $.post(ajaxurl, {
                    action: 'git_setup_check',
                    nonce: wpGitManager.nonce,
                    check_type: 'git_path',
                    git_path: gitPath
                })
                .done(function(response) {
                    if (response.success) {
                        $('#git-path-status').html('<div class="notice notice-success inline"><p>✓ Git found: ' + response.data.version + '</p></div>');
                        $('#check-setup').show();
                        $('#next-step-1').show();
                    } else {
                        $('#git-path-status').html('<div class="notice notice-error inline"><p>✗ ' + response.data + '</p></div>');
                        $('#check-setup').hide();
                        $('#next-step-1').hide();
                    }
                })
                .fail(function() {
                    $('#git-path-status').html('<div class="notice notice-error inline"><p>✗ Failed to test Git path</p></div>');
                    $('#check-setup').hide();
                    $('#next-step-1').hide();
                })
                .always(function() {
                    button.prop('disabled', false).text('Test Git Path');
                });
        },

        handleCheckSetup: function() {
            var gitPath = $('#git-path').val();
            var repoPath = $('#repo-path').val();
            var button = $(this);

            button.prop('disabled', true).text('Checking...');

            $.post(ajaxurl, {
                    action: 'git_setup_check',
                    nonce: wpGitManager.nonce,
                    check_type: 'full_status',
                    git_path: gitPath,
                    repo_path: repoPath
                })
                .done(function(response) {
                    if (response.success) {
                        SetupPage.displaySetupStatus(response.data.status, response.data.recommendations);
                        SetupPage.handleSetupType(response.data.status, response.data.recommendations);
                    }
                })
                .always(function() {
                    button.prop('disabled', false).text('Check Setup Status');
                });
        },

        displaySetupStatus: function (status, recommendations) {
            var html = '<div class="setup-type-info">';
            html += '<h3>' + recommendations.title + '</h3>';
            html += '<p class="setup-description">' + recommendations.description + '</p>';
            html += '</div>';

            html += '<div class="setup-status-details">';
            html += '<h4>Current Status</h4>';

            html += '<div class="status-item ' + (status.git_available ? 'success' : 'error') + '">';
            html += (status.git_available ? '✓' : '✗') + ' Git Available</div>';

            html += '<div class="status-item ' + (status.repo_exists ? 'success' : 'warning') + '">';
            html += (status.repo_exists ? '✓' : '○') + ' Git Repository</div>';

            html += '<div class="status-item ' + (status.user_configured ? 'success' : 'warning') + '">';
            html += (status.user_configured ? '✓' : '○') + ' User Configuration';
            if (status.user_configured && status.user_info) {
                html += ' (' + status.user_info.name + ' &lt;' + status.user_info.email + '&gt;)';
            }
            html += '</div>';

            html += '<div class="status-item ' + (status.has_remote ? 'success' : 'warning') + '">';
            html += (status.has_remote ? '✓' : '○') + ' Remote Repository';
            if (status.has_remote && status.remotes && status.remotes.length > 0) {
                html += ' (' + status.remotes.length + ' configured)';
            }
            html += '</div>';

            html += '<div class="status-item ' + (status.has_branch ? 'success' : 'warning') + '">';
            html += (status.has_branch ? '✓' : '○') + ' Branches';
            if (status.has_branch) {
                html += ' (Current: ' + (status.current_branch || 'Unknown') + ')';
            }
            html += '</div>';

            html += '<div class="status-item ' + (status.has_commits ? 'success' : 'warning') + '">';
            html += (status.has_commits ? '✓' : '○') + ' Commits';
            if (status.has_commits) {
                html += ' (' + status.total_commits + ' total)';
            }
            html += '</div>';

            html += '</div>';

            // Show warnings if any
            if (recommendations.warnings && recommendations.warnings.length > 0) {
                html += '<div class="setup-warnings">';
                html += '<h4>⚠️ Warnings</h4>';
                html += '<ul>';
                recommendations.warnings.forEach(function (warning) {
                    html += '<li>' + warning + '</li>';
                });
                html += '</ul>';
                html += '</div>';
            }

            // Show next steps
            if (recommendations.next_steps && recommendations.next_steps.length > 0) {
                html += '<div class="setup-next-steps">';
                html += '<h4>Next Steps</h4>';
                html += '<ol>';
                recommendations.next_steps.forEach(function (step) {
                    html += '<li>' + step + '</li>';
                });
                html += '</ol>';
                html += '</div>';
            }

            $('#setup-status').html(html);
        },

        handleSetupType: function (status, recommendations) {
            // If setup is already complete, show step 4 (success)
            if (status.is_complete) {
                SetupPage.showStep(4);
                return;
            }

            // Update step 2 description based on setup type
            SetupPage.updateStep2Description(status, recommendations);

            // Handle different setup types
            switch (status.setup_type) {
                case 'fresh':
                    // Fresh setup - proceed to step 2 normally
                    SetupPage.showStep(2);
                    break;

                case 'existing':
                    // Existing repository - show mode selector and pre-populate fields
                    SetupPage.showSetupModeSelector(status);
                    SetupPage.prefillExistingRepoData(status);
                    SetupPage.showStep(2);
                    break;

                case 'partial':
                    // Partial setup - show mode selector and pre-populate fields with warnings
                    SetupPage.showSetupModeSelector(status);
                    SetupPage.prefillExistingRepoData(status);
                    SetupPage.showStep(2);
                    break;
            }
        },

        updateStep2Description: function (status, recommendations) {
            var description = '';

            switch (status.setup_type) {
                case 'fresh':
                    description = 'No existing Git repository found. We\'ll help you create a new one and configure it for your WordPress site.';
                    break;
                case 'existing':
                    description = 'An existing Git repository was detected. Choose how you\'d like to proceed and review the configuration below.';
                    break;
                case 'partial':
                    description = 'A Git repository exists but has some issues. Choose how you\'d like to proceed and review the configuration below.';
                    break;
            }

            $('#step-2-description p').text(description);
        },

        showSetupModeSelector: function (status) {
            $('#setup-mode-selector').removeClass('hidden');

            // Set default mode based on status
            if (status.setup_type === 'existing' && status.user_configured && status.has_branch) {
                $('input[name="setup_mode"][value="configure"]').prop('checked', true);
            } else {
                $('input[name="setup_mode"][value="fresh"]').prop('checked', true);
            }

            // Bind mode change event
            $('input[name="setup_mode"]').on('change', function () {
                SetupPage.handleSetupModeChange($(this).val(), status);
            });
        },

        handleSetupModeChange: function (mode, status) {
            if (mode === 'configure') {
                // Configure existing - pre-fill with existing data
                SetupPage.prefillExistingRepoData(status);
                $('#start-setup').text('Configure Plugin');
            } else {
                // Start fresh - clear fields and use defaults
                SetupPage.clearRepoData();
                $('#start-setup').text('Initialize Repository');
            }
        },

        clearRepoData: function () {
            $('#git-user-name').val('');
            $('#git-user-email').val('');
            $('#branch-name').val('main');
            $('#remote-name').val('origin');
            $('#remote-url').val('');
            $('.existing-repo-notice').remove();
        },

        prefillExistingRepoData: function (status) {
            // Pre-fill user information if available
            if (status.user_configured && status.user_info) {
                $('#git-user-name').val(status.user_info.name);
                $('#git-user-email').val(status.user_info.email);
            }

            // Pre-fill branch information
            if (status.current_branch) {
                $('#branch-name').val(status.current_branch);
            }

            // Pre-fill remote information if available
            if (status.remotes && status.remotes.length > 0) {
                var firstRemote = status.remotes[0];
                $('#remote-name').val(firstRemote.name);
                if (firstRemote.fetch) {
                    $('#remote-url').val(firstRemote.fetch);
                }
            }

            // Show a notice about existing repository
            var notice = '<div class="notice notice-info inline existing-repo-notice">';
            notice += '<p><strong>Existing Repository Detected:</strong> ';
            notice += 'We\'ve pre-filled the form with your current Git configuration. ';
            notice += 'Review and modify as needed.</p>';
            notice += '</div>';

            $('#config-form').prepend(notice);
        },

        handleInitialStatusCheck: function () {
            var button = $(this);
            button.prop('disabled', true).text('Checking...');

            // Use default paths for initial check
            var gitPath = $('#git-path').val() || '/usr/bin/git';
            var repoPath = $('#repo-path').val() || wpGitManager.defaultRepoPath;

            $.post(ajaxurl, {
                action: 'git_setup_check',
                nonce: wpGitManager.nonce,
                check_type: 'full_status',
                git_path: gitPath,
                repo_path: repoPath
            })
                .done(function (response) {
                    if (response.success) {
                        SetupPage.displayInitialStatus(response.data.status, response.data.recommendations);
                        SetupPage.showGitConfigSection(response.data.status);
                    } else {
                        $('#current-config-status').html('<div class="notice notice-error inline"><p>Failed to check setup status</p></div>');
                    }
                })
                .fail(function () {
                    $('#current-config-status').html('<div class="notice notice-error inline"><p>Failed to check setup status</p></div>');
                })
                .always(function () {
                    button.prop('disabled', false).text('Check Current Setup');
                });
        },

        displayInitialStatus: function (status, recommendations) {
            var html = '<div class="initial-status-result">';
            html += '<h4>' + recommendations.title + '</h4>';
            html += '<p>' + recommendations.description + '</p>';

            // Show key status items
            html += '<div class="status-summary">';
            html += '<div class="status-item ' + (status.git_available ? 'success' : 'error') + '">';
            html += (status.git_available ? '✓' : '✗') + ' Git Available</div>';

            html += '<div class="status-item ' + (status.repo_exists ? 'success' : 'info') + '">';
            html += (status.repo_exists ? '✓' : '○') + ' Git Repository</div>';

            if (status.repo_exists) {
                html += '<div class="status-item ' + (status.user_configured ? 'success' : 'warning') + '">';
                html += (status.user_configured ? '✓' : '○') + ' User Configuration</div>';

                html += '<div class="status-item ' + (status.has_branch ? 'success' : 'warning') + '">';
                html += (status.has_branch ? '✓' : '○') + ' Branches</div>';

                html += '<div class="status-item ' + (status.has_commits ? 'success' : 'info') + '">';
                html += (status.has_commits ? '✓' : '○') + ' Commits</div>';
            }
            html += '</div>';

            // Show warnings if any
            if (recommendations.warnings && recommendations.warnings.length > 0) {
                html += '<div class="status-warnings">';
                html += '<h5>⚠️ Issues Found:</h5>';
                html += '<ul>';
                recommendations.warnings.forEach(function (warning) {
                    html += '<li>' + warning + '</li>';
                });
                html += '</ul>';
                html += '</div>';
            }

            html += '</div>';
            $('#current-config-status').html(html);
        },

        showGitConfigSection: function (status) {
            $('#git-config-section').removeClass('hidden');

            // If Git is not available or there are issues, show the config section immediately
            if (!status.git_available || status.setup_type === 'partial') {
                // Config section is already shown
            } else if (status.is_complete) {
                // Setup is complete, can proceed directly
                $('#check-setup').show();
                $('#next-step-1').show();
            } else {
                // Show test git button
                $('#test-git').show();
            }
        },

        updateProgress: function(step, status, message) {
            var element = $('#progress-' + step);
            element.removeClass('in-progress success error');

            if (status === 'in-progress') {
                element.addClass('in-progress').text('⟳ ' + message);
            } else if (status === 'success') {
                element.addClass('success').text('✓ ' + message);
            } else if (status === 'error') {
                element.addClass('error').text('✗ ' + message);
            }
        },

        handleStartSetup: function() {
            var userName = $('#git-user-name').val();
            var userEmail = $('#git-user-email').val();
            var branchName = $('#branch-name').val();
            var remoteName = $('#remote-name').val();
            var remoteUrl = $('#remote-url').val();

            if (!userName || !userEmail || !branchName) {
                alert('Please fill in all required fields (User Name, User Email, and Branch Name)');
                return;
            }

            var button = $(this);
            button.prop('disabled', true).text('Setting up...');

            // Hide form and show progress
            $('#config-form').hide();
            $('#setup-progress').show();

            // Start the setup process
            SetupPage.startSetupProcess({
                gitPath: $('#git-path').val(),
                repoPath: $('#repo-path').val(),
                userName: userName,
                userEmail: userEmail,
                branchName: branchName,
                remoteName: remoteName,
                remoteUrl: remoteUrl
            });
        },

        startSetupProcess: function(config) {
            // Step 1: Initialize repository with branch
            SetupPage.updateProgress('init', 'in-progress', 'Initializing repository with branch "' + config.branchName + '"...');

            $.post(ajaxurl, {
                    action: 'git_init_repo_with_branch',
                    nonce: wpGitManager.nonce,
                    git_path: config.gitPath,
                    repo_path: config.repoPath,
                    branch_name: config.branchName
                })
                .done(function(response) {
                    if (response.success) {
                        SetupPage.updateProgress('init', 'success', 'Repository initialized with branch "' + config.branchName + '"');
                        SetupPage.setupUserConfig(config);
                    } else {
                        SetupPage.updateProgress('init', 'error', 'Failed to initialize repository: ' + response.data);
                        SetupPage.resetSetupForm();
                    }
                })
                .fail(function() {
                    SetupPage.updateProgress('init', 'error', 'Failed to initialize repository');
                    SetupPage.resetSetupForm();
                });
        },

        setupUserConfig: function(config) {
            SetupPage.updateProgress('user', 'in-progress', 'Setting user configuration...');

            $.post(ajaxurl, {
                    action: 'git_set_user',
                    nonce: wpGitManager.nonce,
                    git_path: config.gitPath,
                    repo_path: config.repoPath,
                    user_name: config.userName,
                    user_email: config.userEmail
                })
                .done(function(response) {
                    if (response.success) {
                        SetupPage.updateProgress('user', 'success', 'User configuration set');
                        if (config.remoteUrl) {
                            SetupPage.setupRemote(config);
                        } else {
                            SetupPage.updateProgress('remote', 'success', 'Remote setup skipped');
                            SetupPage.createInitialFiles(config);
                        }
                    } else {
                        SetupPage.updateProgress('user', 'error', 'Failed to set user configuration: ' + response.data);
                        SetupPage.resetSetupForm();
                    }
                })
                .fail(function() {
                    SetupPage.updateProgress('user', 'error', 'Failed to set user configuration');
                    SetupPage.resetSetupForm();
                });
        },

        setupRemote: function(config) {
            SetupPage.updateProgress('remote', 'in-progress', 'Adding remote repository...');

            $.post(ajaxurl, {
                    action: 'git_add_remote',
                    nonce: wpGitManager.nonce,
                    git_path: config.gitPath,
                    repo_path: config.repoPath,
                    remote_name: config.remoteName,
                    remote_url: config.remoteUrl
                })
                .done(function(response) {
                    if (response.success) {
                        SetupPage.updateProgress('remote', 'success', 'Remote repository added');
                        SetupPage.createInitialFiles(config);
                    } else {
                        SetupPage.updateProgress('remote', 'error', 'Failed to add remote: ' + response.data);
                        SetupPage.resetSetupForm();
                    }
                })
                .fail(function() {
                    SetupPage.updateProgress('remote', 'error', 'Failed to add remote repository');
                    SetupPage.resetSetupForm();
                });
        },

        createInitialFiles: function(config) {
            SetupPage.updateProgress('files', 'in-progress', 'Creating initial files and commit...');

            $.post(ajaxurl, {
                    action: 'git_create_initial_commit',
                    nonce: wpGitManager.nonce,
                    git_path: config.gitPath,
                    repo_path: config.repoPath,
                    branch_name: config.branchName
                })
                .done(function(response) {
                    if (response.success) {
                        SetupPage.updateProgress('files', 'success', 'Initial files created and committed');
                        SetupPage.completeSetup(config);
                    } else {
                        SetupPage.updateProgress('files', 'error', 'Failed to create initial files: ' + response.data);
                        SetupPage.resetSetupForm();
                    }
                })
                .fail(function() {
                    SetupPage.updateProgress('files', 'error', 'Failed to create initial files');
                    SetupPage.resetSetupForm();
                });
        },

        completeSetup: function(config) {
            // Save all settings
            var settings = {
                git_path: config.gitPath,
                repo_path: config.repoPath,
                remote_name: config.remoteName || 'origin',
                branch_name: config.branchName,
                auto_add: '1'
            };

            $.post(ajaxurl, {
                    action: 'git_setup_check',
                    nonce: wpGitManager.nonce,
                    check_type: 'save_settings',
                    settings: settings
                })
                .done(function(response) {
                    if (response.success) {
                        // After saving settings, proceed to step 3 for final configuration
                        setTimeout(function() {
                            SetupPage.showStep(3);
                        }, 1000);
                    } else {
                        alert('Error saving settings: ' + response.data);
                        SetupPage.resetSetupForm();
                    }
                })
                .fail(function() {
                    alert('Failed to save settings');
                    SetupPage.resetSetupForm();
                });
        },

        resetSetupForm: function() {
            $('#setup-progress').hide();
            $('#config-form').show();
            $('#start-setup').prop('disabled', false).text('Initialize Repository');
        },

        handleCompleteSetup: function() {
            var settings = {
                git_path: $('#git-path').val(),
                repo_path: $('#repo-path').val(),
                remote_name: $('#remote-name').val() || 'origin',
                branch_name: $('#branch-name').val() || 'main',
                auto_add: $('#auto-add').is(':checked') ? '1' : '0'
            };

            // Create .gitignore if content provided
            var gitignoreContent = $('#gitignore-content').val();
            if (gitignoreContent) {
                settings.gitignore_content = gitignoreContent;
            }

            var button = $(this);
            button.prop('disabled', true).text('Completing...');

            $.post(ajaxurl, {
                    action: 'git_setup_check',
                    nonce: wpGitManager.nonce,
                    check_type: 'save_settings',
                    settings: settings
                })
                .done(function(response) {
                    if (response.success) {
                        // After saving settings, verify setup is actually complete
                        SetupPage.verifySetupCompletion();
                    } else {
                        alert('Error saving settings: ' + response.data);
                    }
                })
                .always(function () {
                    button.prop('disabled', false).text('Complete Setup');
                });
        },

        verifySetupCompletion: function () {
            var gitPath = $('#git-path').val();
            var repoPath = $('#repo-path').val();

            $.post(ajaxurl, {
                action: 'git_setup_check',
                nonce: wpGitManager.nonce,
                check_type: 'full_status',
                git_path: gitPath,
                repo_path: repoPath
            })
                .done(function (response) {
                    if (response.success && response.data.is_complete) {
                        // Only show completion if setup is actually complete
                        SetupPage.showStep(4);
                    } else {
                        // Setup is not complete, show appropriate message
                        var missingItems = [];
                        if (!response.data.git_available) missingItems.push('Git not available');
                        if (!response.data.repo_exists) missingItems.push('Git repository not initialized');
                        if (!response.data.user_configured) missingItems.push('User configuration missing');
                        if (!response.data.has_branch) missingItems.push('No branches found');

                        alert('Setup is not complete. Missing: ' + missingItems.join(', ') + '. Please complete the setup process first.');
                    }
                })
                .fail(function () {
                    alert('Failed to verify setup completion. Please try again.');
                });
        }
    };

    // Initialize SetupPage
    SetupPage.init();
});
