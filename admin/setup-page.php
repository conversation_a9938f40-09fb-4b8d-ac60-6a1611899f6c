<?php

/**
 * Setup page for WP Git Manager
 */

if (!current_user_can('manage_options')) {
    wp_die('Unauthorized');
}
?>

<div class="wrap">
    <div class="setup-breadcrumb">
        <p>
            <a href="<?php echo admin_url('admin.php?page=wp-git-manager'); ?>">Git Manager</a> &raquo;
            <a href="<?php echo admin_url('admin.php?page=wp-git-manager-settings'); ?>">Settings</a> &raquo;
            <strong>Setup Guide</strong>
        </p>
    </div>

    <h1>Git Manager Setup</h1>

    <div id="setup-container">
        <!-- Step Progress Indicator -->
        <div class="setup-progress-indicator">
            <div class="step-indicator active" data-step="1">
                <span class="step-number">1</span>
                <span class="step-title">Git Configuration</span>
            </div>
            <div class="step-indicator" data-step="2">
                <span class="step-number">2</span>
                <span class="step-title">Repository Setup</span>
            </div>
            <div class="step-indicator" data-step="3">
                <span class="step-number">3</span>
                <span class="step-title">Final Configuration</span>
            </div>
            <div class="step-indicator" data-step="4">
                <span class="step-number">4</span>
                <span class="step-title">Setup Complete</span>
            </div>
        </div>

        <div class="setup-step active" id="step-1">
            <h2>Step 1: Git Configuration</h2>
            <p>First, let's verify that Git is available on your server.</p>

            <table class="form-table">
                <tr>
                    <th scope="row">Git Path</th>
                    <td>
                        <input type="text" id="git-path" class="regular-text" value="<?php echo esc_attr(get_option('wpgm_git_path', '/usr/bin/git')); ?>" />
                        <p class="description">Path to the Git executable on your server.</p>
                        <div id="git-path-status"></div>
                    </td>
                </tr>
                <tr>
                    <th scope="row">Repository Path</th>
                    <td>
                        <input type="text" id="repo-path" class="regular-text" value="<?php echo esc_attr(get_option('wpgm_repo_path', ABSPATH)); ?>" />
                        <p class="description">Path to your WordPress installation directory.</p>
                    </td>
                </tr>
            </table>

            <div class="setup-navigation">
                <button type="button" class="button button-primary" id="test-git">Test Git Path</button>
                <button type="button" class="button hidden" id="check-setup">Check Setup Status</button>
                <button type="button" class="button button-primary hidden" id="next-step-1">Next Step</button>
            </div>
        </div>

        <div class="setup-step" id="step-2">
            <h2>Step 2: Repository Configuration</h2>
            <p>Please provide the following information to set up your Git repository:</p>

            <div id="setup-status"></div>

            <div id="config-form">
                <h3>Git User Configuration</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row">User Name</th>
                        <td>
                            <input type="text" id="git-user-name" class="regular-text" placeholder="Your Name" required />
                            <p class="description">Your name for Git commits</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">User Email</th>
                        <td>
                            <input type="email" id="git-user-email" class="regular-text" placeholder="<EMAIL>" required />
                            <p class="description">Your email for Git commits</p>
                        </td>
                    </tr>
                </table>

                <h3>Initial Branch Configuration</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row">Branch Name</th>
                        <td>
                            <input type="text" id="branch-name" class="regular-text" value="main" required />
                            <p class="description">Name for the initial branch (e.g., main, master, develop)</p>
                        </td>
                    </tr>
                </table>

                <h3>Remote Repository (Optional)</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row">Remote Name</th>
                        <td>
                            <input type="text" id="remote-name" class="regular-text" value="origin" />
                            <p class="description">Name for the remote repository</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Remote URL</th>
                        <td>
                            <input type="url" id="remote-url" class="regular-text" placeholder="https://github.com/username/repo.git" />
                            <p class="description">Optional: Add a remote repository URL (GitHub, GitLab, etc.)</p>
                        </td>
                    </tr>
                </table>

                <div class="setup-navigation">
                    <button type="button" class="button" id="prev-step-2">Previous</button>
                    <button type="button" class="button button-primary" id="start-setup">Initialize Repository</button>
                </div>
            </div>

            <div id="setup-progress" class="hidden">
                <h3>Setting up repository...</h3>
                <div id="progress-status">
                    <div class="progress-item" id="progress-init">○ Initializing repository...</div>
                    <div class="progress-item" id="progress-user">○ Setting user configuration...</div>
                    <div class="progress-item" id="progress-remote">○ Adding remote repository...</div>
                    <div class="progress-item" id="progress-files">○ Creating initial files...</div>
                </div>
                <div class="setup-navigation">
                    <button type="button" class="button" id="prev-step-2-progress">Previous</button>
                </div>
            </div>
        </div>

        <div class="setup-step" id="step-3">
            <h2>Step 3: Final Configuration</h2>
            <p>Save your settings and complete the setup.</p>

            <div id="final-settings">
                <table class="form-table">
                    <tr>
                        <th scope="row">Auto-add Files</th>
                        <td>
                            <label>
                                <input type="checkbox" id="auto-add" checked />
                                Automatically add all files when committing
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Exclude Files</th>
                        <td>
                            <textarea id="gitignore-content" rows="10" cols="50" placeholder="wp-config.php&#10;*.log&#10;node_modules/&#10;.DS_Store"></textarea>
                            <p class="description">Files and patterns to exclude from Git (will create/update .gitignore)</p>
                        </td>
                    </tr>
                </table>
            </div>

            <div class="setup-navigation">
                <button type="button" class="button" id="prev-step-3">Previous</button>
                <button type="button" class="button button-primary" id="complete-setup">Complete Setup</button>
            </div>
        </div>

        <div class="setup-step" id="step-4">
            <h2>🎉 Setup Complete!</h2>
            <p class="setup-success-message">Congratulations! Your Git repository has been successfully set up and is now ready to use.</p>

            <div class="setup-summary">
                <h3>What's been configured:</h3>
                <ul>
                    <li>✓ Git repository initialized</li>
                    <li>✓ User configuration set</li>
                    <li>✓ Initial branch created</li>
                    <li>✓ Initial commit made</li>
                    <li>✓ Plugin settings saved</li>
                </ul>
            </div>

            <div class="setup-navigation">
                <h3>What would you like to do next?</h3>
                <p>
                    <a href="<?php echo admin_url('admin.php?page=wp-git-manager'); ?>" class="button button-primary button-large">
                        <span class="dashicons dashicons-dashboard"></span> Go to Dashboard
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=wp-git-manager-settings'); ?>" class="button button-large">
                        <span class="dashicons dashicons-admin-settings"></span> Go to Settings
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=wp-git-manager-manage'); ?>" class="button button-large">
                        <span class="dashicons dashicons-admin-tools"></span> Manage Repository
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>

<?php
// Enqueue setup-page-specific CSS
wp_enqueue_style(
    'wp-git-manager-setup',
    plugin_dir_url(__FILE__) . '../assets/setup-page.css',
    array('wp-git-manager-css'),
    '1.0.0'
);
?>

<?php
// Enqueue setup-page-specific JavaScript
wp_enqueue_script(
    'wp-git-manager-setup-page',
    plugin_dir_url(__FILE__) . '../assets/setup-page.js',
    array('jquery', 'wp-git-manager-main'),
    '1.0.0',
    true
);
?>