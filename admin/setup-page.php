<?php

/**
 * Setup page for WP Git Manager
 */

if (!current_user_can('manage_options')) {
    wp_die('Unauthorized');
}
?>

<div class="wrap">
    <div class="setup-breadcrumb">
        <p>
            <a href="<?php echo admin_url('admin.php?page=wp-git-manager'); ?>">Git Manager</a> &raquo;
            <a href="<?php echo admin_url('admin.php?page=wp-git-manager-settings'); ?>">Settings</a> &raquo;
            <strong>Setup Guide</strong>
        </p>
    </div>

    <h1>Git Manager Setup</h1>

    <div id="setup-container">
        <!-- Step Progress Indicator -->
        <div class="setup-progress-indicator">
            <div class="step-indicator active" data-step="1">
                <span class="step-number">1</span>
                <span class="step-title">Git Configuration</span>
            </div>
            <div class="step-indicator" data-step="2">
                <span class="step-number">2</span>
                <span class="step-title">Repository Setup</span>
            </div>
            <div class="step-indicator" data-step="3">
                <span class="step-number">3</span>
                <span class="step-title">Final Configuration</span>
            </div>
            <div class="step-indicator" data-step="4">
                <span class="step-number">4</span>
                <span class="step-title">Setup Complete</span>
            </div>
        </div>

        <div class="setup-step active" id="step-1">
            <h2>Step 1: Current Setup Status</h2>
            <p>Let's check your current Git configuration and setup status.</p>

            <div id="setup-status-display">
                <div class="status-loading">
                    <p>Checking current setup status...</p>
                    <div class="spinner"></div>
                </div>
            </div>

            <div id="setup-actions" class="setup-actions-section hidden">
                <!-- Actions will be populated based on setup status -->
            </div>
        </div>

        <div class="setup-step" id="step-2">
            <h2>Step 2: Setup Execution</h2>
            <p>Performing setup actions based on your configuration...</p>

            <div id="setup-progress">
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div class="progress-text" id="progress-text">Initializing...</div>
                </div>
            </div>

            <div id="setup-results">
                <div id="setup-log" class="setup-log"></div>
            </div>

            <div class="setup-navigation hidden" id="step-2-navigation">
                <button type="button" class="button" id="prev-step-2">Previous</button>
                <button type="button" class="button button-primary" id="complete-setup">Complete Setup</button>
            </div>
        </div>

        <div class="setup-step" id="step-3">
            <h2>🎉 Setup Complete!</h2>
            <p class="setup-success-message">Congratulations! Your Git repository has been successfully set up and is now ready to use.</p>

            <div class="setup-summary">
                <h3>What's been configured:</h3>
                <ul>
                    <li>✓ Git repository initialized</li>
                    <li>✓ User configuration set</li>
                    <li>✓ Initial branch created</li>
                    <li>✓ Initial commit made</li>
                    <li>✓ Plugin settings saved</li>
                </ul>
            </div>

            <div class="setup-navigation">
                <h3>What would you like to do next?</h3>
                <p>
                    <a href="<?php echo admin_url('admin.php?page=wp-git-manager'); ?>" class="button button-primary button-large">
                        <span class="dashicons dashicons-dashboard"></span> Go to Dashboard
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=wp-git-manager-settings'); ?>" class="button button-large">
                        <span class="dashicons dashicons-admin-settings"></span> Go to Settings
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=wp-git-manager-manage'); ?>" class="button button-large">
                        <span class="dashicons dashicons-admin-tools"></span> Manage Repository
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>

<?php
// Enqueue setup-page-specific CSS
wp_enqueue_style(
    'wp-git-manager-setup',
    plugin_dir_url(__FILE__) . '../assets/setup-page.css',
    array('wp-git-manager-css'),
    '1.0.0'
);
?>

<?php
// Enqueue setup-page-specific JavaScript
wp_enqueue_script(
    'wp-git-manager-setup-page',
    plugin_dir_url(__FILE__) . '../assets/setup-page.js',
    array('jquery', 'wp-git-manager-main'),
    '1.0.0',
    true
);
?>