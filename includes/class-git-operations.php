<?php

/**
 * Git operations class for WP Git Manager
 * Enhanced with czproject/git-php library
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

use CzProject\GitPhp\Git;
use CzProject\GitPhp\GitRepository;
use CzProject\GitPhp\GitException;
use CzProject\GitPhp\Runners\CliRunner;

class WPGitManager_GitOperations {

    private $git_repo;
    private $repo_path;
    private $git_path;

    public function __construct()
    {
        $this->git_path = get_option('wpgm_git_path', '/usr/bin/git');
        $this->repo_path = get_option('wpgm_repo_path', ABSPATH);

        $this->initialize_git_repository();
    }

    /**
     * Initialize Git repository instance if not already initialized
     * @param string|null $git_path Optional git path override
     * @param string|null $repo_path Optional repository path override
     * @return bool True if repository was initialized successfully
     */
    private function initialize_git_repository($git_path = null, $repo_path = null)
    {
        $git_path = $git_path ?: $this->git_path;
        $repo_path = $repo_path ?: $this->repo_path;

        try {
            if (is_dir($repo_path . '/.git')) {
                $runner = new CliRunner($git_path);
                $this->git_repo = new GitRepository($repo_path, $runner);
                return true;
            }
        } catch (GitException $e) {
            error_log('WP Git Manager: Failed to initialize Git repository: ' . $e->getMessage());
        }

        return false;
    }

    public function get_status($git_path = null, $repo_path = null)
    {
        if (!$this->is_git_available()) {
            return array('changes_count' => 0, 'status' => 'Git not available', 'files' => array());
        }

        if (!$this->git_repo) {
            return array('changes_count' => 0, 'status' => 'Repository not initialized', 'files' => array());
        }

        try {
            // Use git status --porcelain to get machine-readable output
            $status_output = $this->git_repo->execute('status', '--porcelain');
            $changes = array();
            $files = array();
            $changes_count = 0;

            if (!empty($status_output)) {
                foreach ($status_output as $line) {
                    if (trim($line) !== '') {
                        $changes[] = trim($line);
                        $changes_count++;

                        // Parse the status line
                        $status_code = substr($line, 0, 2);
                        $file_name = trim(substr($line, 3));

                        // Handle quoted file names (Git quotes file names with special characters)
                        if (strlen($file_name) > 0 && $file_name[0] === '"' && substr($file_name, -1) === '"') {
                            // Remove quotes and decode escape sequences
                            $file_name = substr($file_name, 1, -1);
                            $file_name = stripcslashes($file_name);
                        }

                        // Determine status
                        $status = 'M'; // Modified by default
                        if ($status_code[0] === 'A' || $status_code[1] === 'A') {
                            $status = 'A'; // Added
                        } elseif ($status_code[0] === 'D' || $status_code[1] === 'D') {
                            $status = 'D'; // Deleted
                        } elseif ($status_code[0] === 'R' || $status_code[1] === 'R') {
                            $status = 'R'; // Renamed
                        } elseif ($status_code[0] === '?' || $status_code[1] === '?') {
                            $status = '?'; // Untracked
                        }

                        $files[] = array(
                            'name' => $file_name,
                            'status' => $status,
                            'raw_status' => $status_code,
                            'id' => $this->generate_file_id($file_name)
                        );
                    }
                }
            }

            return array(
                'changes_count' => $changes_count,
                'status' => 'ok',
                'changes' => $changes,
                'files' => $files
            );
        } catch (GitException $e) {
            return array('changes_count' => 0, 'status' => 'Exception: ' . $e->getMessage(), 'files' => array());
        }
    }
    
    public function is_git_available() {
        $git_path = get_option('wpgm_git_path', '/usr/bin/git');
        return file_exists($git_path) && is_executable($git_path);
    }

    public function execute_command($command)
    {
        if (!$this->is_git_available()) {
            return array('success' => false, 'message' => 'Git not available');
        }

        if (!$this->git_repo) {
            return array('success' => false, 'message' => 'Repository not initialized');
        }

        try {
            $parts = explode(' ', trim($command));
            $git_command = array_shift($parts);

            switch ($git_command) {
                case 'add':
                    if (in_array('-A', $parts) || in_array('--all', $parts)) {
                        $this->git_repo->addAllChanges();
                        return array('success' => true, 'message' => 'All changes added');
                    } else {
                        foreach ($parts as $file) {
                            $this->git_repo->addFile($file);
                        }
                        return array('success' => true, 'message' => 'Files added');
                    }

                case 'commit':
                    $message_index = array_search('-m', $parts);
                    if ($message_index !== false && isset($parts[$message_index + 1])) {
                        $message = $parts[$message_index + 1];
                        $this->git_repo->commit($message);
                        return array('success' => true, 'message' => 'Committed successfully');
                    }
                    return array('success' => false, 'message' => 'Commit message required');

                case 'push':
                    $remote = $parts[0] ?? 'origin';
                    if (isset($parts[1])) {
                        $this->git_repo->push([$remote, $parts[1]]);
                    } else {
                        $this->git_repo->push($remote);
                    }
                    return array('success' => true, 'message' => 'Pushed successfully');

                case 'pull':
                    $remote = $parts[0] ?? 'origin';
                    if (isset($parts[1])) {
                        $this->git_repo->pull([$remote, $parts[1]]);
                    } else {
                        $this->git_repo->pull($remote);
                    }
                    return array('success' => true, 'message' => 'Pulled successfully');

                default:
                    // For other commands, pass the parts as separate arguments
                    array_unshift($parts, $git_command);
                    $output = call_user_func_array(array($this->git_repo, 'execute'), $parts);
                    return array('success' => true, 'message' => implode("\n", $output));
            }
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Git error: ' . $e->getMessage());
        } catch (Exception $e) {
            return array('success' => false, 'message' => 'Exception: ' . $e->getMessage());
        }
    }

    public function check_setup_status()
    {
        $status = array(
            'is_complete' => false,
            'git_available' => false,
            'repo_exists' => false,
            'user_configured' => false,
            'has_remote' => false,
            'has_branch' => false,
            'has_commits' => false,
            'setup_type' => 'fresh', // fresh, existing, partial
            'details' => array(),
            'current_branch' => '',
            'total_commits' => 0,
            'remotes' => array(),
            'branches' => array(),
            'user_info' => array(),
            'last_commit' => array(),
            'working_directory_clean' => true,
            'issues' => array()
        );

        $status['git_available'] = $this->is_git_available();

        if (!$status['git_available']) {
            $status['issues'][] = 'Git executable not found or not accessible';
            return $status;
        }

        // Check if .git directory exists
        if (is_dir($this->repo_path . '/.git')) {
            $status['repo_exists'] = true;
            $status['setup_type'] = 'existing';

            try {
                // Try to initialize git repository if not already done
                if (!$this->git_repo) {
                    $this->initialize_git_repository();
                }

                if ($this->git_repo) {
                    // Get detailed user configuration
                    try {
                        $user_name_output = $this->git_repo->execute('config', 'user.name');
                        $user_email_output = $this->git_repo->execute('config', 'user.email');

                        $user_name = is_array($user_name_output) ? trim($user_name_output[0] ?? '') : trim($user_name_output ?? '');
                        $user_email = is_array($user_email_output) ? trim($user_email_output[0] ?? '') : trim($user_email_output ?? '');

                        if (!empty($user_name) && !empty($user_email)) {
                            $status['user_configured'] = true;
                            $status['user_info'] = array(
                                'name' => $user_name,
                                'email' => $user_email
                            );
                        } else {
                            if (empty($user_name)) $status['issues'][] = 'Git user name not configured';
                            if (empty($user_email)) $status['issues'][] = 'Git user email not configured';
                        }
                    } catch (GitException $e) {
                        $status['issues'][] = 'Could not read user configuration: ' . $e->getMessage();
                    }

                    // Get remotes information
                    try {
                        $remotes_output = $this->git_repo->execute('remote', '-v');
                        if (!empty($remotes_output)) {
                            $status['has_remote'] = true;
                            $remotes = array();
                            foreach ($remotes_output as $line) {
                                if (preg_match('/^(\S+)\s+(\S+)\s+\((\w+)\)$/', $line, $matches)) {
                                    $remote_name = $matches[1];
                                    $remote_url = $matches[2];
                                    $type = $matches[3];

                                    if (!isset($remotes[$remote_name])) {
                                        $remotes[$remote_name] = array('name' => $remote_name);
                                    }
                                    $remotes[$remote_name][$type] = $remote_url;
                                }
                            }
                            $status['remotes'] = array_values($remotes);
                        }
                    } catch (GitException $e) {
                        $status['issues'][] = 'Could not read remotes: ' . $e->getMessage();
                    }

                    // Get branches information
                    try {
                        $branches = $this->git_repo->getBranches();
                        if (!empty($branches)) {
                            $status['has_branch'] = true;
                            $status['branches'] = $branches;

                            // Get current branch
                            try {
                                $current_branch_output = $this->git_repo->execute('branch', '--show-current');
                                $current_branch = is_array($current_branch_output) ? trim($current_branch_output[0] ?? '') : trim($current_branch_output ?? '');
                                $status['current_branch'] = $current_branch;
                            } catch (GitException $e) {
                                // Fallback method for older Git versions
                                try {
                                    $branch_output = $this->git_repo->execute('branch');
                                    foreach ($branch_output as $line) {
                                        if (strpos($line, '*') === 0) {
                                            $status['current_branch'] = trim(substr($line, 1));
                                            break;
                                        }
                                    }
                                } catch (GitException $e2) {
                                    $status['issues'][] = 'Could not determine current branch';
                                }
                            }
                        } else {
                            $status['issues'][] = 'No branches found in repository';
                        }
                    } catch (GitException $e) {
                        $status['issues'][] = 'Could not read branches: ' . $e->getMessage();
                    }

                    // Check for commits
                    try {
                        $log_output = $this->git_repo->execute('log', '--oneline', '--max-count=1');
                        if (!empty($log_output)) {
                            $status['has_commits'] = true;

                            // Get total commit count
                            try {
                                $count_output = $this->git_repo->execute('rev-list', '--count', 'HEAD');
                                $count_value = is_array($count_output) ? trim($count_output[0] ?? '0') : trim($count_output ?? '0');
                                $status['total_commits'] = intval($count_value);
                            } catch (GitException $e) {
                                $status['total_commits'] = 1; // At least one commit exists
                            }

                            // Get last commit info
                            try {
                                $hash_output = $this->git_repo->execute('rev-parse', 'HEAD');
                                $message_output = $this->git_repo->execute('log', '-1', '--pretty=format:%s');
                                $author_output = $this->git_repo->execute('log', '-1', '--pretty=format:%an');
                                $date_output = $this->git_repo->execute('log', '-1', '--pretty=format:%ci');

                                $last_commit_hash = is_array($hash_output) ? trim($hash_output[0] ?? '') : trim($hash_output ?? '');
                                $last_commit_message = is_array($message_output) ? trim($message_output[0] ?? '') : trim($message_output ?? '');
                                $last_commit_author = is_array($author_output) ? trim($author_output[0] ?? '') : trim($author_output ?? '');
                                $last_commit_date = is_array($date_output) ? trim($date_output[0] ?? '') : trim($date_output ?? '');

                                $status['last_commit'] = array(
                                    'hash' => $last_commit_hash,
                                    'message' => $last_commit_message,
                                    'author' => $last_commit_author,
                                    'date' => $last_commit_date
                                );
                            } catch (GitException $e) {
                                $status['issues'][] = 'Could not read last commit info';
                            }
                        } else {
                            $status['issues'][] = 'Repository has no commits';
                        }
                    } catch (GitException $e) {
                        // This is expected for empty repositories
                        $status['details'][] = 'Repository is empty (no commits)';
                    }

                    // Check working directory status
                    try {
                        $status_output = $this->git_repo->execute('status', '--porcelain');
                        $status['working_directory_clean'] = empty($status_output);
                        if (!$status['working_directory_clean']) {
                            $status['details'][] = 'Working directory has uncommitted changes';
                        }
                    } catch (GitException $e) {
                        $status['issues'][] = 'Could not check working directory status';
                    }
                }
            } catch (GitException $e) {
                $status['issues'][] = 'Git repository error: ' . $e->getMessage();
                $status['setup_type'] = 'partial';
            }
        } else {
            $status['setup_type'] = 'fresh';
            $status['details'][] = 'No Git repository found';
        }

        // Determine if setup is complete and setup type
        $status['is_complete'] = $status['git_available'] && $status['repo_exists'] &&
                                $status['user_configured'] && $status['has_branch'];

        // Refine setup type based on completeness
        if ($status['repo_exists'] && !$status['is_complete']) {
            $status['setup_type'] = 'partial';
        }

        return $status;
    }

    /**
     * Get setup recommendations based on current status
     * @return array Setup recommendations and next steps
     */
    public function get_setup_recommendations()
    {
        $status = $this->check_setup_status();
        $recommendations = array(
            'type' => $status['setup_type'],
            'title' => '',
            'description' => '',
            'next_steps' => array(),
            'warnings' => array(),
            'can_proceed' => true
        );

        switch ($status['setup_type']) {
            case 'fresh':
                $recommendations['title'] = 'Fresh Git Setup';
                $recommendations['description'] = 'No Git repository found. We\'ll help you create a new repository and configure it for your WordPress site.';
                $recommendations['next_steps'] = array(
                    'Initialize a new Git repository',
                    'Configure Git user settings',
                    'Create initial branch',
                    'Make first commit with current files',
                    'Optionally add remote repository'
                );
                break;

            case 'existing':
                $recommendations['title'] = 'Existing Git Repository Detected';
                $recommendations['description'] = 'Found an existing Git repository. We\'ll help you configure the plugin to work with it.';

                if ($status['is_complete']) {
                    $recommendations['description'] .= ' Your setup appears to be complete!';
                    $recommendations['next_steps'] = array(
                        'Verify plugin settings',
                        'Test Git operations',
                        'You\'re ready to use Git Manager!'
                    );
                } else {
                    $missing = array();
                    if (!$status['user_configured']) $missing[] = 'user configuration';
                    if (!$status['has_branch']) $missing[] = 'branches';
                    if (!$status['has_commits']) $missing[] = 'initial commit';

                    $recommendations['description'] .= ' Missing: ' . implode(', ', $missing) . '.';
                    $recommendations['next_steps'] = array();

                    if (!$status['user_configured']) {
                        $recommendations['next_steps'][] = 'Configure Git user name and email';
                    }
                    if (!$status['has_branch']) {
                        $recommendations['next_steps'][] = 'Create or checkout a branch';
                    }
                    if (!$status['has_commits']) {
                        $recommendations['next_steps'][] = 'Make initial commit';
                    }
                    $recommendations['next_steps'][] = 'Complete plugin configuration';
                }

                // Add repository info
                if (!empty($status['current_branch'])) {
                    $recommendations['next_steps'][] = 'Current branch: ' . $status['current_branch'];
                }
                if ($status['total_commits'] > 0) {
                    $recommendations['next_steps'][] = 'Total commits: ' . $status['total_commits'];
                }
                if (!empty($status['remotes'])) {
                    $recommendations['next_steps'][] = 'Remotes configured: ' . count($status['remotes']);
                }
                break;

            case 'partial':
                $recommendations['title'] = 'Partial Git Setup Detected';
                $recommendations['description'] = 'Found a Git repository but there are some issues that need to be resolved.';

                if (!empty($status['issues'])) {
                    $recommendations['warnings'] = $status['issues'];
                    $recommendations['description'] .= ' Issues found: ' . implode(', ', $status['issues']);
                }

                $recommendations['next_steps'] = array(
                    'Review and fix any issues',
                    'Complete missing configuration',
                    'Verify repository integrity'
                );

                if (!$status['user_configured']) {
                    $recommendations['next_steps'][] = 'Configure Git user settings';
                }
                if (!$status['has_branch']) {
                    $recommendations['next_steps'][] = 'Create or fix branches';
                }
                break;
        }

        if (!$status['working_directory_clean']) {
            $recommendations['warnings'][] = 'Working directory has uncommitted changes';
        }

        return $recommendations;
    }

    /**
     * Repair common Git repository issues
     * @param string $git_path Path to Git executable
     * @param string $repo_path Path to repository
     * @return array Result of repair operation
     */
    public function repair_repository($git_path, $repo_path)
    {
        try {
            if (!$this->git_repo) {
                $this->initialize_git_repository($git_path, $repo_path);
            }

            $repairs_performed = array();
            $issues_found = array();

            // Check if repository is corrupted
            try {
                $this->git_repo->execute('fsck', '--full');
                $repairs_performed[] = 'Repository integrity check passed';
            } catch (GitException $e) {
                $issues_found[] = 'Repository integrity issues found: ' . $e->getMessage();

                // Try to repair with git fsck --full --auto
                try {
                    $this->git_repo->execute('fsck', '--full', '--auto');
                    $repairs_performed[] = 'Attempted automatic repository repair';
                } catch (GitException $e2) {
                    $issues_found[] = 'Could not auto-repair repository';
                }
            }

            // Check and fix index issues
            try {
                $this->git_repo->execute('status', '--porcelain');
                $repairs_performed[] = 'Index status check passed';
            } catch (GitException $e) {
                $issues_found[] = 'Index issues found';

                // Try to reset the index
                try {
                    $this->git_repo->execute('reset', '--mixed', 'HEAD');
                    $repairs_performed[] = 'Reset index to HEAD';
                } catch (GitException $e2) {
                    $issues_found[] = 'Could not reset index';
                }
            }

            // Check for missing HEAD
            try {
                $this->git_repo->execute('rev-parse', 'HEAD');
                $repairs_performed[] = 'HEAD reference is valid';
            } catch (GitException $e) {
                $issues_found[] = 'Missing or invalid HEAD reference';

                // Try to create an initial commit if no commits exist
                try {
                    $branches = $this->git_repo->getBranches();
                    if (empty($branches)) {
                        // Create initial commit
                        $readme_file = $repo_path . '/README.md';
                        if (!file_exists($readme_file)) {
                            file_put_contents($readme_file, "# Repository\n\nThis repository was repaired by WP Git Manager.\n");
                        }

                        $this->git_repo->addFile('README.md');
                        $this->git_repo->commit('Initial commit (repository repair)');
                        $repairs_performed[] = 'Created initial commit to fix HEAD';
                    }
                } catch (GitException $e2) {
                    $issues_found[] = 'Could not create initial commit';
                }
            }

            // Check for proper branch setup
            try {
                $current_branch = $this->git_repo->getCurrentBranchName();
                $repairs_performed[] = 'Current branch: ' . $current_branch;
            } catch (GitException $e) {
                $issues_found[] = 'No current branch found';

                // Try to create and checkout main branch
                try {
                    $this->git_repo->createBranch('main', true);
                    $repairs_performed[] = 'Created and checked out main branch';
                } catch (GitException $e2) {
                    $issues_found[] = 'Could not create main branch';
                }
            }

            // Clean up any temporary files
            try {
                $this->git_repo->execute('clean', '-fd');
                $repairs_performed[] = 'Cleaned up temporary files';
            } catch (GitException $e) {
                // This is not critical, just log it
                $issues_found[] = 'Could not clean temporary files (non-critical)';
            }

            // Garbage collection to optimize repository
            try {
                $this->git_repo->execute('gc', '--auto');
                $repairs_performed[] = 'Performed garbage collection';
            } catch (GitException $e) {
                $issues_found[] = 'Could not perform garbage collection (non-critical)';
            }

            $message = 'Repository repair completed. ';
            $message .= count($repairs_performed) . ' repairs performed';
            if (!empty($issues_found)) {
                $message .= ', ' . count($issues_found) . ' issues found';
            }

            return array(
                'success' => true,
                'message' => $message,
                'repairs_performed' => $repairs_performed,
                'issues_found' => $issues_found
            );
        } catch (GitException $e) {
            return array(
                'success' => false,
                'message' => 'Repository repair failed: ' . $e->getMessage()
            );
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Error during repair: ' . $e->getMessage()
            );
        }
    }

    public function init_repository($git_path, $repo_path)
    {
        try {
            $runner = new CliRunner($git_path);
            $git = new Git($runner);
            $repo = $git->init($repo_path);

            // Update instance variables
            $this->git_path = $git_path;
            $this->repo_path = $repo_path;
            $this->git_repo = $repo;

            return array('success' => true, 'message' => 'Repository initialized');
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Failed to initialize repository: ' . $e->getMessage());
        } catch (Exception $e) {
            return array('success' => false, 'message' => 'Error: ' . $e->getMessage());
        }
    }

    public function init_repository_with_branch($git_path, $repo_path, $branch_name)
    {
        try {
            $runner = new CliRunner($git_path);

            // Use direct command execution to support --initial-branch
            $result = $runner->run($repo_path, [
                'init',
                '--initial-branch=' . $branch_name,
                '.'
            ]);

            if (!$result->isOk()) {
                throw new GitException("Git init failed with exit code " . $result->getExitCode());
            }

            // Update instance variables
            $this->git_path = $git_path;
            $this->repo_path = $repo_path;
            $this->git_repo = new GitRepository($repo_path, $runner);

            return array('success' => true, 'message' => 'Repository initialized with branch "' . $branch_name . '"');
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Failed to initialize repository: ' . $e->getMessage());
        } catch (Exception $e) {
            return array('success' => false, 'message' => 'Error: ' . $e->getMessage());
        }
    }

    public function set_user_config($git_path, $repo_path, $user_name, $user_email)
    {
        try {
            if (!$this->git_repo) {
                $this->initialize_git_repository($git_path, $repo_path);
            }

            $this->git_repo->execute('config', 'user.name', $user_name);
            $this->git_repo->execute('config', 'user.email', $user_email);

            return array('success' => true, 'message' => 'User configuration set');
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Failed to set user configuration: ' . $e->getMessage());
        } catch (Exception $e) {
            return array('success' => false, 'message' => 'Error: ' . $e->getMessage());
        }
    }

    public function add_remote($git_path, $repo_path, $remote_name, $remote_url)
    {
        try {
            if (!$this->git_repo) {
                $this->initialize_git_repository($git_path, $repo_path);
            }

            $this->git_repo->addRemote($remote_name, $remote_url);

            return array('success' => true, 'message' => 'Remote added successfully');
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Failed to add remote: ' . $e->getMessage());
        } catch (Exception $e) {
            return array('success' => false, 'message' => 'Error: ' . $e->getMessage());
        }
    }

    public function create_initial_commit($git_path, $repo_path, $branch_name)
    {
        try {
            if (!$this->git_repo) {
                $this->initialize_git_repository($git_path, $repo_path);
            }

            // Create initial files
            $readme_file = $repo_path . '/README.md';
            if (!file_exists($readme_file)) {
                file_put_contents($readme_file, "# WordPress Site\n\nThis repository contains a WordPress installation managed with WP Git Manager.\n\nBranch: " . $branch_name . "\n");
            }

            // Create .gitignore if it doesn't exist
            $gitignore_file = $repo_path . '/.gitignore';
            if (!file_exists($gitignore_file)) {
                $gitignore_content = "wp-config.php\n*.log\nnode_modules/\n.DS_Store\n/wp-content/uploads/\n/wp-content/cache/\n";
                file_put_contents($gitignore_file, $gitignore_content);
            }

            // Add files and create initial commit
            $this->git_repo->addFile('README.md');
            if (file_exists($gitignore_file)) {
                $this->git_repo->addFile('.gitignore');
            }
            $this->git_repo->commit('Initial commit');

            return array('success' => true, 'message' => 'Initial commit created');
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Failed to create initial commit: ' . $e->getMessage());
        } catch (Exception $e) {
            return array('success' => false, 'message' => 'Error: ' . $e->getMessage());
        }
    }

    public function create_branch($git_path, $repo_path, $branch_name)
    {
        try {
            if (!$this->git_repo) {
                $this->initialize_git_repository($git_path, $repo_path);
            }

            // Check if there are staged changes that need to be committed
            $status_output = $this->git_repo->execute('status', '--porcelain');
            $has_staged_changes = false;
            $has_unstaged_changes = false;

            foreach ($status_output as $line) {
                if (!empty($line)) {
                    $status_code = substr($line, 0, 2);
                    // Check for staged changes (first character is not space or ?)
                    if ($status_code[0] !== ' ' && $status_code[0] !== '?') {
                        $has_staged_changes = true;
                    }
                    // Check for unstaged changes (second character is not space)
                    if ($status_code[1] !== ' ') {
                        $has_unstaged_changes = true;
                    }
                }
            }

            // If there are staged changes, commit them first
            if ($has_staged_changes) {
                $this->git_repo->commit('WP Git Manager: Auto-commit staged changes before branch creation');
            }

            // If there are unstaged changes, add and commit them
            if ($has_unstaged_changes) {
                $this->git_repo->addAllChanges();
                $this->git_repo->commit('WP Git Manager: Auto-commit unstaged changes before branch creation');
            }

            // If no commits exist yet, create an initial commit
            try {
                $this->git_repo->getLastCommit();
            } catch (GitException $e) {
                // No commits exist, create initial commit
                $readme_file = $repo_path . '/README.md';
                if (!file_exists($readme_file)) {
                    file_put_contents($readme_file, "# WordPress Site\n\nThis repository contains a WordPress installation managed with WP Git Manager.\n");
                }
                $this->git_repo->addFile('README.md');
                $this->git_repo->commit('Initial commit');
            }

            // Create and switch to branch
            $this->git_repo->createBranch($branch_name, true);

            return array('success' => true, 'message' => 'Branch created and checked out');
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Failed to create branch: ' . $e->getMessage());
        } catch (Exception $e) {
            return array('success' => false, 'message' => 'Error: ' . $e->getMessage());
        }
    }
    
    public function test_git_path($git_path) {
        if (file_exists($git_path) && is_executable($git_path)) {
            exec($git_path . ' --version 2>/dev/null', $output, $return);
            if ($return === 0) {
                return array('success' => true, 'version' => $output[0]);
            }
        }
        return array('success' => false, 'message' => 'Git not found or not executable');
    }

    // Enhanced methods using czproject/git-php

    public function get_branches()
    {
        if (!$this->git_repo) {
            return array('success' => false, 'message' => 'Repository not initialized');
        }

        try {
            $branches = $this->git_repo->getBranches();
            $current_branch = $this->git_repo->getCurrentBranchName();

            return array(
                'success' => true,
                'branches' => $branches,
                'current_branch' => $current_branch
            );
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Error getting branches: ' . $e->getMessage());
        }
    }

    public function get_commit_history($limit = 10)
    {
        if (!$this->git_repo) {
            return array('success' => false, 'message' => 'Repository not initialized');
        }

        try {
            // Use detailed format with hash, subject, author name, author email, and date
            $log = $this->git_repo->execute('log', '--pretty=format:%H|%s|%an|%ae|%ad|%ar', '--date=short', '-' . $limit);
            $commits = array();

            foreach ($log as $line) {
                $parts = explode('|', $line, 6);
                if (count($parts) === 6) {
                    $commits[] = array(
                        'hash' => $parts[0],
                        'message' => $parts[1],
                        'author' => $parts[2],
                        'email' => $parts[3],
                        'date' => $parts[4],
                        'relative_date' => $parts[5]
                    );
                }
            }

            return array('success' => true, 'commits' => $commits);
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Error getting commit history: ' . $e->getMessage());
        }
    }

    public function get_file_diff($file_path)
    {
        if (!$this->git_repo) {
            return array('success' => false, 'message' => 'Repository not initialized');
        }

        try {
            $diff = $this->git_repo->execute('diff', $file_path);
            return array('success' => true, 'diff' => implode("\n", $diff));
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Error getting file diff: ' . $e->getMessage());
        }
    }

    public function get_commit_changes($commit_hash)
    {
        if (!$this->git_repo) {
            return array('success' => false, 'message' => 'Repository not initialized');
        }

        try {
            // Use git show to get the commit changes with diff
            $changes = $this->git_repo->execute('show', '--pretty=format:%H%n%s%n%an <%ae>%n%ad%n', '--date=short', $commit_hash);
            return array('success' => true, 'changes' => implode("\n", $changes));
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Error getting commit changes: ' . $e->getMessage());
        }
    }

    public function stage_file($file_identifier)
    {
        if (!$this->git_repo) {
            return array('success' => false, 'message' => 'Repository not initialized');
        }

        try {
            // Debug logging
            error_log('WP Git Manager: stage_file called with identifier: ' . $file_identifier);

            // Check if this is a file ID or direct path
            if (strpos($file_identifier, 'file_') === 0) {
                // This is a file ID, resolve it to the actual path
                $file_path = $this->resolve_file_id($file_identifier);
                error_log('WP Git Manager: Resolved file ID to path: ' . ($file_path !== false ? $file_path : 'FAILED'));
                if ($file_path === false) {
                    return array('success' => false, 'message' => 'Invalid file ID: ' . $file_identifier);
                }
            } else {
                // This is a direct file path (for backward compatibility)
                // Clean the file path more thoroughly
                $file_path = $this->clean_file_path($file_identifier);
                error_log('WP Git Manager: Cleaned file path from "' . $file_identifier . '" to "' . $file_path . '"');
            }

            // Use git add command directly instead of GitPHP's addFile method
            // This is more reliable for files with special characters
            error_log('WP Git Manager: Executing git add for: ' . $file_path);
            $this->git_repo->execute('add', $file_path);
            return array('success' => true, 'message' => 'File staged successfully: ' . basename($file_path));
        } catch (GitException $e) {
            error_log('WP Git Manager: Git exception in stage_file: ' . $e->getMessage());
            return array('success' => false, 'message' => 'Error staging file: ' . $e->getMessage());
        }
    }

    public function unstage_file($file_identifier)
    {
        if (!$this->git_repo) {
            return array('success' => false, 'message' => 'Repository not initialized');
        }

        try {
            // Check if this is a file ID or direct path
            if (strpos($file_identifier, 'file_') === 0) {
                // This is a file ID, resolve it to the actual path
                $file_path = $this->resolve_file_id($file_identifier);
                if ($file_path === false) {
                    return array('success' => false, 'message' => 'Invalid file ID: ' . $file_identifier);
                }
            } else {
                // This is a direct file path (for backward compatibility)
                // Clean the file path more thoroughly
                $file_path = $this->clean_file_path($file_identifier);
            }

            $this->git_repo->execute('reset', 'HEAD', $file_path);
            return array('success' => true, 'message' => 'File unstaged successfully: ' . basename($file_path));
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Error unstaging file: ' . $e->getMessage());
        }
    }

    public function switch_branch($branch_name)
    {
        if (!$this->git_repo) {
            return array('success' => false, 'message' => 'Repository not initialized');
        }

        try {
            $this->git_repo->checkout($branch_name);
            return array('success' => true, 'message' => 'Switched to branch: ' . $branch_name);
        } catch (GitException $e) {
            return array('success' => false, 'message' => 'Error switching branch: ' . $e->getMessage());
        }
    }

    /**
     * Reset/remove the .git directory to start fresh
     * This will completely remove all Git history and configuration
     *
     * @param string $repo_path Path to the repository
     * @param bool $clear_all_data Whether to clear all plugin data or just git-specific data
     * @return array Success status and message
     */
    public function reset_repository($repo_path = null, $clear_all_data = false)
    {
        try {
            if ($repo_path === null) {
                $repo_path = $this->repo_path;
            }

            $git_dir = $repo_path . '/.git';
            $git_exists = is_dir($git_dir);

            if ($git_exists) {
                // Recursively remove the .git directory
                $this->remove_directory_recursive($git_dir);
            }

            // Clear the current git_repo instance
            $this->git_repo = null;

            if ($clear_all_data) {
                // Clear ALL plugin options
                $this->clear_all_plugin_data($repo_path);
                $message = 'Git repository and all plugin data reset successfully. Plugin has been completely reset to initial state.';
            } else {
                // Clear only basic Git setup options
                delete_option('wpgm_needs_setup');
                delete_option('wpgm_repo_path');
                delete_option('wpgm_git_path');
                $message = $git_exists ?
                    'Git repository reset successfully. All Git history and configuration removed.' :
                    'Plugin data cleared successfully. No Git repository was found.';
            }

            return array('success' => true, 'message' => $message);
        } catch (Exception $e) {
            return array('success' => false, 'message' => 'Error resetting repository: ' . $e->getMessage());
        }
    }

    /**
     * Clear all plugin data including settings and .gitignore
     *
     * @param string $repo_path Repository path for .gitignore removal
     */
    private function clear_all_plugin_data($repo_path)
    {
        // List of all plugin options to clear
        $plugin_options = array(
            'wpgm_needs_setup',
            'wpgm_git_path',
            'wpgm_repo_path',
            'wpgm_remote_name',
            'wpgm_branch_name',
            'wpgm_auto_add',
            'wpgm_commit_author_name',
            'wpgm_commit_author_email',
            'wpgm_default_commit_message'
        );

        // Delete all plugin options
        foreach ($plugin_options as $option) {
            delete_option($option);
        }

        // Remove .gitignore file if it exists
        $gitignore_path = rtrim($repo_path, '/') . '/.gitignore';
        if (file_exists($gitignore_path)) {
            unlink($gitignore_path);
        }

        // Set the needs_setup flag to true to trigger setup wizard
        update_option('wpgm_needs_setup', true);
    }

    /**
     * Recursively remove a directory and all its contents
     *
     * @param string $dir Directory path to remove
     * @return bool Success status
     */
    private function remove_directory_recursive($dir)
    {
        if (!is_dir($dir)) {
            return false;
        }

        $files = array_diff(scandir($dir), array('.', '..'));

        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->remove_directory_recursive($path);
            } else {
                // Handle read-only files (common in .git directory)
                if (!is_writable($path)) {
                    chmod($path, 0666);
                }
                unlink($path);
            }
        }

        return rmdir($dir);
    }

    /**
     * Check if a Git repository exists in the specified path
     *
     * @param string $repo_path Path to check
     * @return bool True if .git directory exists
     */
    public function repository_exists($repo_path = null)
    {
        if ($repo_path === null) {
            $repo_path = $this->repo_path;
        }

        return is_dir($repo_path . '/.git');
    }

    /**
     * Get repository information for display
     *
     * @return array Repository information
     */
    public function get_repository_info()
    {
        $info = array(
            'exists' => false,
            'path' => $this->repo_path,
            'git_path' => $this->git_path,
            'size' => 0,
            'branch' => null,
            'last_commit' => null,
            'remotes' => array()
        );

        if ($this->repository_exists()) {
            $info['exists'] = true;

            // Get .git directory size
            $git_dir = $this->repo_path . '/.git';
            $info['size'] = $this->get_directory_size($git_dir);

            if ($this->git_repo) {
                try {
                    // Get current branch
                    $info['branch'] = $this->git_repo->getCurrentBranchName();

                    // Get last commit info
                    $last_commit = $this->git_repo->getLastCommit();
                    $info['last_commit'] = array(
                        'id' => substr($last_commit->getId()->toString(), 0, 8),
                        'subject' => $last_commit->getSubject(),
                        'author' => $last_commit->getAuthorName(),
                        'date' => $last_commit->getAuthorDate()->format('Y-m-d H:i:s')
                    );

                    // Get remotes
                    $remotes = $this->git_repo->execute('remote', '-v');
                    $info['remotes'] = $remotes;
                } catch (GitException $e) {
                    // If we can't get info, that's okay, just leave fields null
                }
            }
        }

        return $info;
    }

    public function get_current_branch($git_path = null, $repo_path = null)
    {
        if (!$this->git_repo) {
            return array('current' => 'Unknown');
        }

        try {
            $current_branch = $this->git_repo->getCurrentBranchName();
            return array('current' => $current_branch);
        } catch (GitException $e) {
            return array('current' => 'Unknown');
        }
    }

    public function get_commit_count($git_path = null, $repo_path = null)
    {
        if (!$this->git_repo) {
            return 0;
        }

        try {
            $log = $this->git_repo->execute('rev-list', '--count', 'HEAD');
            return !empty($log) ? intval($log[0]) : 0;
        } catch (GitException $e) {
            return 0;
        }
    }

    public function get_recent_commits($git_path = null, $repo_path = null, $limit = 5)
    {
        if (!$this->git_repo) {
            return array();
        }

        try {
            $log = $this->git_repo->execute('log', '--pretty=format:%H|%s|%an|%ad', '--date=short', '-' . $limit);
            $commits = array();

            foreach ($log as $line) {
                $parts = explode('|', $line, 4);
                if (count($parts) === 4) {
                    $commits[] = array(
                        'hash' => $parts[0],
                        'message' => $parts[1],
                        'author' => $parts[2],
                        'date' => $parts[3]
                    );
                }
            }

            return $commits;
        } catch (GitException $e) {
            return array();
        }
    }

    public function get_remotes($git_path = null, $repo_path = null)
    {
        if (!$this->git_repo) {
            return array();
        }

        try {
            $remotes_output = $this->git_repo->execute('remote', '-v');
            $remotes = array();
            $processed = array();

            foreach ($remotes_output as $line) {
                if (preg_match('/^(\S+)\s+(\S+)\s+\(fetch\)$/', $line, $matches)) {
                    $name = $matches[1];
                    $url = $matches[2];

                    if (!in_array($name, $processed)) {
                        $remotes[] = array(
                            'name' => $name,
                            'url' => $url
                        );
                        $processed[] = $name;
                    }
                }
            }

            return $remotes;
        } catch (GitException $e) {
            return array();
        }
    }

    /**
     * Calculate directory size recursively
     *
     * @param string $dir Directory path
     * @return int Size in bytes
     */
    private function get_directory_size($dir)
    {
        $size = 0;

        if (!is_dir($dir)) {
            return 0;
        }

        foreach (new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)) as $file) {
            $size += $file->getSize();
        }

        return $size;
    }

    /**
     * Format bytes to human readable format
     *
     * @param int $bytes Size in bytes
     * @return string Formatted size
     */
    public function format_bytes($bytes)
    {
        $units = array('B', 'KB', 'MB', 'GB');
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Generate a unique ID for a file based on its path
     *
     * @param string $file_path The file path
     * @return string Unique file ID
     */
    private function generate_file_id($file_path)
    {
        // Create a unique ID based on the file path
        // Use base64 encoding to make it URL-safe and avoid special characters
        return 'file_' . str_replace(array('+', '/', '='), array('-', '_', ''), base64_encode($file_path));
    }

    /**
     * Resolve a file ID back to the original file path
     *
     * @param string $file_id The file ID
     * @return string|false The original file path or false if invalid
     */
    public function resolve_file_id($file_id)
    {
        // Remove the 'file_' prefix and decode
        if (strpos($file_id, 'file_') !== 0) {
            return false;
        }

        $encoded_path = substr($file_id, 5); // Remove 'file_' prefix
        $encoded_path = str_replace(array('-', '_'), array('+', '/'), $encoded_path);

        // Add padding if needed for base64 decoding
        $padding = 4 - (strlen($encoded_path) % 4);
        if ($padding !== 4) {
            $encoded_path .= str_repeat('=', $padding);
        }

        $decoded_path = base64_decode($encoded_path, true);
        return $decoded_path !== false ? $decoded_path : false;
    }

    /**
     * Clean and normalize a file path for Git operations
     *
     * @param string $file_path The raw file path
     * @return string The cleaned file path
     */
    private function clean_file_path($file_path)
    {
        // Remove surrounding quotes and whitespace
        $file_path = trim($file_path);
        $file_path = trim($file_path, '"\'');

        // Remove any leading/trailing whitespace again after quote removal
        $file_path = trim($file_path);

        // Normalize path separators (convert backslashes to forward slashes)
        $file_path = str_replace('\\', '/', $file_path);

        // Remove any double slashes
        $file_path = preg_replace('#/+#', '/', $file_path);

        return $file_path;
    }
}